<template>
  <view class="container">
    <scroll-view scroll-y class="content">
      <!-- 基本信息 -->
      <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form :model="retailOrderInfo" :rules="rules" ref="uForm" labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="客户" prop="customer_name" borderBottom required>
              <u--input v-model="retailOrderInfo.customer_name" border="none" placeholder="请选择客户" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(1)">
              </u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input v-model="retailOrderInfo.out_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="retailOrderInfo.order_no" border="none" placeholder="提交后自动生成" inputAlign="right"
                disabled disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="关联订单" borderBottom>
              <u-row>
                <u-col :span="retailOrderInfo.related_order_code ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.related_order_code" border="none" placeholder="请选择关联订单"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openRelatedOrder()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeRelatedOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.related_order_code" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓库" prop="warehouse_name" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.warehouse_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.warehouse_name" border="none" placeholder="请选择仓库"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openInventorySelection()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouseName">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.warehouse_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 商品清单 -->
      <merch-bill
        :items="retailOrderInfo.items"
        type="wholesale"
        :is-view="isView"
        :purchaseOrderName="retailOrderInfo.related_order_code"
        @add-goods="addGoods"
        @add-association-goods="addAssociationGoods"
        @open-product-details="openProductDetails"
        @amount-change="handleAmountChange" />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePaymentMethodName">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="合计金额" borderBottom>
              <u--input v-model="retailOrderInfo.total_amount" border="none" placeholder="0" inputAlign="right" disabled
                disabledColor="#fff" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="优惠率(%)" borderBottom>
              <u--input v-model="retailOrderInfo.discountRate" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="#fff" @input="updateDiscountRate" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款优惠" borderBottom>
              <u--input v-model="retailOrderInfo.payment_method_discount" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="#fff" @input="updateDiscount" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款金额" borderBottom>
              <u--input v-model="retailOrderInfo.pay_amount" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="#fff" />
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="retailOrderInfo.remark" placeholder="请输入备注" autoHeight border="none"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <view>
        <imageUpload />
      </view>

      <!-- 底部操作栏 -->
      <view class="operation" v-if="!isView">
        <u-button type="primary" text="保存" @click="save"></u-button>
      </view>
    </scroll-view>
    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>
    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="4" />

    <!-- 保存提示框 -->
    <view>
      <u-modal :show="promptShow" :title="promptTitle" :content="promptContent" :showCancelButton="true"
        :closeOnClickOverlay="true" @confirm="addWholesaleOrder" @cancel="close" @close="close"></u-modal>
    </view>

    <!-- 客户选择器 -->
    <searchSelector :selectorShow.sync="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectCustomer" @close="closePopup" @customerScrollToLower="getCustomersList" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
      @close="closeInventorySelection" @save="confirmInventory" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />

    <!-- 商品详细信息弹出层 -->
    <productDetails :productDetailsShow="productDetailsShow" :type="6" :productData="productData"
      :isOpenFromOrder="true" :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing"
      @close="closeProductDetails" @confirm="handleProductDetails" />
    
    <!-- 关联订单选择弹出层 -->
    <!-- <wholesaleAssociation ref="wholesaleAssociation" /> -->
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { getCustomerList } from "@/api/customer";
import { addRetailOrders } from "@/api/retailOrders";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import { getGoodsDetail } from "@/api/goods";
import eventBus from "@/utils/eventBus";
import wholesaleAssociation from "@/pages/wholesaleOrders/editWholesaleOrders/wholesaleAssociation.vue";

// 类型定义
interface OrderItem {
  item: string | number;
  item_name: string;
  name?: string;
  id?: string | number;
  code: string;
  unit: string | number;
  unit_name: string;
  quantity: number;
  price: number;
  purchase_price?: number;
  total: number;
  conversion_rate?: number;
  remaining_stock?: number;
  total_stock?: number;
  from_sales_order?: boolean;
  sales_order_item?: boolean;
}

interface CustomerItem {
  id: string | number;
  name: string;
}

interface ProductData {
  item: string | number;
  item_name: string;
  code: string;
  unit: string | number;
  unit_name: string;
  quantity: number;
  price: number;
  purchase_price?: number;
  total: number;
  remaining_stock?: number;
  total_stock?: number;
}

interface AmountData {
  actualAmount: string | number;
  discount: string | number;
  totalAmount: string | number;
}

interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data?: T;
}

const uForm = ref<any>(null);

const isView = ref(false); //是否为修改进入
const promptShow = ref(false); //提示显示状态
const promptTitle = ref("温馨提示"); //提示标题
const promptContent = ref("保存后将无法修改，是否确认保存？"); //保存提示内容
const productDetailsShow = ref(false); //商品详细信息弹出层
const productData = ref({
  item: "",
  item_name: "",
  code: "",
  unit: "",
  unit_name: "",
  quantity: 0,
  price: 0,
  total: 0,
  remaining_stock: 0,
  total_stock: 0,
  from_sales_order: false,
  sales_order_item: false,

}); //物品信息
const accountSelectShow = ref(false);
const isExpandGoodsList = ref(false);
const activeQtyIndex = ref(-1);
const inventorySelectionShow = ref(false); // 仓库选择弹出层显示状态
const selectType = ref(0); //搜索类型 0 客户
const selectorShow = ref(false); //客户选择框展示状态
const selectList = ref<any[]>([]); //客户选择框数据
const customerList = ref<any[]>([]); //客户列表
const isCustomerMore = ref(true); //客户列表是否有更多数据
const customerPageParams = reactive({
  //获取客户列表的参数
  page: 1,
  page_size: 20,
});
interface OrderItem {
  item: number;
  item_name: string;
  name?: string;
  id?: string | number;
  code: string;
  unit: string | number;
  unit_name: string;
  quantity: number;
  price: number;
  purchase_price?: number;
  total: number;
  conversion_rate?: number;
  remaining_stock?: number;
  total_stock?: number;
}
const retailOrderInfo = reactive<any>({
  document_type: "wholesale",
  customer_name: "",
  customer: "",
  out_date: "",
  order_no: "",
  related_order_code: "", // 关联订单编号
  related_order_name: "", // 关联订单名称
  related_order_id: "", // 关联订单ID
  sales_order: "", // 关联的销售订单ID（用于API提交）
  warehouse: "",
  warehouse_name: "",
  handler: "", // 操作员ID
  handler_name: "", // 操作员名称
  items: [],
  total_amount: 0,
  discountRate: 0, //优惠率
  discount: 0, //商品清单优惠金额
        payment_method_discount: 0, //收款优惠
  total_sale_price: 0,
        pay_amount: 0, //收款金额
  payment_method: "",
  payment_method_name: "",
  remark: "",
  is_draft: false,
});
const rules = reactive<any>({
  customer_name: {
    type: "string",
    required: true,
    message: "请选择客户",
    trigger: ["blur", "change"],
  },
  warehouse_name: {
    type: "string",
    required: true,
    message: "请选择仓库",
    trigger: ["blur", "change"],
  },
});

onReady(() => {
  //onReady 为uni-app支持的生命周期之一
  uForm.value?.setRules(rules);
});

onMounted(() => {
    getCustomersList();

    // 注册事件处理
    eventBus.$on("selectGoodsList", handleSelectGoodsList);
    eventBus.$on("relatedOrderSelected", handleRelatedOrder);
    eventBus.$on("relatedOrderItemsSelected", handleRelatedOrderItems);

    console.log("editWholesaleOrders 已挂载，正在检查是否有缓存的商品数据");

    // 延迟一下检查是否有缓存的商品列表数据
    setTimeout(() => {
      try {
        const cachedGoodsListStr = uni.getStorageSync('selected_goods_list');
        if (cachedGoodsListStr) {
          console.log("发现缓存的商品列表数据");
          const cachedGoodsList = JSON.parse(cachedGoodsListStr);
          if (Array.isArray(cachedGoodsList) && cachedGoodsList.length > 0) {
            console.log("从缓存中恢复商品列表，商品数量:", cachedGoodsList.length);
            handleSelectGoodsList(cachedGoodsList);

            // 清除缓存，避免重复添加
            uni.removeStorageSync('selected_goods_list');
          }
        } else {
          console.log("没有找到缓存的商品列表数据");
        }
      } catch (e) {
        console.error("读取缓存的商品列表失败:", e);
      }
    }, 500);
});

onShow(() => {
  console.log("editWholesaleOrders 页面显示，再次检查缓存");

  // 页面显示时再次检查缓存
  try {
    const cachedGoodsListStr = uni.getStorageSync('selected_goods_list');
    if (cachedGoodsListStr) {
      console.log("onShow: 发现缓存的商品列表数据");
      const cachedGoodsList = JSON.parse(cachedGoodsListStr);
      if (Array.isArray(cachedGoodsList) && cachedGoodsList.length > 0) {
        console.log("onShow: 从缓存中恢复商品列表，商品数量:", cachedGoodsList.length);
        handleSelectGoodsList(cachedGoodsList);

        // 清除缓存，避免重复添加
        uni.removeStorageSync('selected_goods_list');
      }
    }
  } catch (e) {
    console.error("onShow: 读取缓存的商品列表失败:", e);
  }
});

onBeforeUnmount(() => {
  eventBus.$off("selectGoodsList", handleSelectGoodsList);
  eventBus.$off("relatedOrderSelected", handleRelatedOrder);
  eventBus.$off("relatedOrderItemsSelected", handleRelatedOrderItems);
  // document.removeEventListener('click', handleGlobalClick);
});

onLoad((options :any ) => {
    console.log('编辑页面 onLoad options:', options);
  
    // 如果有传入订单ID，则加载订单详情进行编辑
    if (options.data) {
      try {
        // 先解码 URL 编码的数据，再解析 JSON
        const decodedData = decodeURIComponent(options.data);
        console.log('解码后的数据:', decodedData);

        const data = JSON.parse(decodedData);
        console.log('解析后的订单数据:', data);

        Object.assign(retailOrderInfo, data);

        // 計算和設置各種金額字段
        // total_amount: 合計金額
        retailOrderInfo.total_amount = Number(data.discount) + Number(data.total_sale_price);

        // discount: 商品清單優惠金額
        retailOrderInfo.discount = Number(data.discount);

        // total_sale_price: 商品清單實際金額
        retailOrderInfo.total_sale_price = Number(data.total_sale_price);

        // discountRate: 優惠率
        if (retailOrderInfo.total_amount > 0) {
          retailOrderInfo.discountRate = (Number(data.discount) / retailOrderInfo.total_amount * 100).toFixed(2);
        } else {
          retailOrderInfo.discountRate = 0;
        }

        // payment_method_discount: 收款優惠
        retailOrderInfo.payment_method_discount = Number(data.payment_method_discount || data.discount);

        // pay_amount: 收款金額
        retailOrderInfo.pay_amount = Number(data.pay_amount || data.total_sale_price);

        console.log('編輯模式初始化金額字段:', {
          total_amount: retailOrderInfo.total_amount,
          discount: retailOrderInfo.discount,
          total_sale_price: retailOrderInfo.total_sale_price,
          discountRate: retailOrderInfo.discountRate,
          payment_method_discount: retailOrderInfo.payment_method_discount,
          pay_amount: retailOrderInfo.pay_amount
        });

        // 设置页面标题为编辑模式
        uni.setNavigationBarTitle({
          title: "编辑批发订单",
        });
        isView.value = true;

        console.log('编辑模式初始化完成');
      } catch (error) {
        console.error('解析订单数据失败:', error);
        uni.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
        // 解析失败时，按新增模式处理
        uni.setNavigationBarTitle({
          title: "新增批发订单",
        });
      }
    } else {
      // 设置页面标题为新增模式
      console.log('新增模式初始化');
      uni.setNavigationBarTitle({
        title: "新增批发订单",
      });
    }
});

// 关闭商品详情弹出层
const closeProductDetails = () => {
  productDetailsShow.value = false;
};
// 打开商品详情弹出层
const openProductDetails = (item: any) => {
  if (isView.value) {
    uni.showToast({
      title: '保存后禁止修改',
      icon: "none",
    })
    return;
  }

  // 直接循环retailOrderInfo.items，计算相同id的商品总用量
  let totalUsedStock = 0;
  retailOrderInfo.items.forEach((items: any) => {
    if (items.item === item.item) {
      totalUsedStock += Number(items.quantity || 0) * Number(items.conversion_rate || 1);
    }
  });
  getGoodsDetail(item.item).then((res: any) => {
    if (res.code == 0) {
      productData.value = { ...item, ...res.data };
      productData.value.remaining_stock = item.remaining_stock;
      console.log(productData.value);
      productDetailsShow.value = true;
    }
  });
};



//处理商品详细提交回来的数据
const handleProductDetails = (data: ProductData) => {
  console.log(data);
  console.log(retailOrderInfo.items);
  data.price = Number(data.purchase_price) || 0;
  data.quantity = Number(data.quantity) || 1;
  data.total = Number(data.total) || 0;
  const targetIndex = retailOrderInfo.items.findIndex(
    (infoItem: any) => infoItem.item === data.item && infoItem.unit === data.unit
  );

  if (targetIndex !== -1) {
    console.log('匹配到相同商品和单位类型，更新数据');
    console.log(retailOrderInfo.items[targetIndex]);

    // 使用 $set 确保响应式更新
    retailOrderInfo.items[targetIndex] = data;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === data.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === data.code) {
        item.remaining_stock = totalUsedStock;
      }
    });

    console.log('更新后的数据:', retailOrderInfo.items[targetIndex]);
  } else {
    console.log('未找到匹配的商品');
  }

  // calcTotalAndDiscount();
};
//商品清单删除商品
const handleDelBtnPricing = (data: OrderItem) => {
  retailOrderInfo.items = retailOrderInfo.items.filter(
    (item: any) => item.item !== data.item
  );
  productDetailsShow.value = false;
  // calcTotalAndDiscount();
};

//选择商品
const handleSelectGoodsList = (data: OrderItem[]) => {
  console.log("选择商品数据：", data);
  console.log("数据类型:", typeof data, "是否数组:", Array.isArray(data), "长度:", data ? data.length : 0);

  if (!data || data.length === 0) {
    console.warn("接收到空的商品数据");
    return;
  }

  // 初始化retailOrderInfo.items数组如果不存在
  if (!retailOrderInfo.items) {
    retailOrderInfo.items = [];
    console.log("初始化商品数组");
  }

  // 將選中的商品數據轉換為銷售訂單所需格式
  const formattedItems = data.map((item) => {
    const formattedItem = {
      ...item,
      // 確保商品數據字段與salesOrder.items結構匹配
      // 重新映射並轉換為數字類型
      // 優先使用item.price(銷貨單類型type=10會設置此字段)，其次使用purchase_price
      price: Number(item.price || item.purchase_price) || 0,
      quantity: Number(item.quantity) || 1,
      total: Number(item.total) || (Number(item.quantity || 1) * Number(item.price || item.purchase_price || 0)),
      // 確保商品必須字段
      item_name: item.item_name || item.name,
      // 修改：確保 item 字段正確設置，優先使用 API 返回的 id
      item: item.id || item.item || item.code,
      code: item.code || '',
      unit_name: item.unit_name || '个',
      // 保留銷售訂單標記
      from_sales_order: item.from_sales_order || false,
      sales_order_item: item.sales_order_item || false
    };
    console.log("格式化後的單個商品:", formattedItem);
    return formattedItem;
  });

  console.log("格式化後的商品數據:", formattedItems);
  console.log("格式化後的商品數量:", formattedItems.length);

  // 遍历新选择的商品，进行合并或添加
  formattedItems.forEach((newItem: any) => {
    // 查找是否已存在相同商品和相同单位的项目
    // 确保提取所有可能的标识符
        const newItemIdentifiers = [
          newItem.id,
          newItem.item,
          newItem.code,
          newItem.item_id
        ].filter(Boolean); // 过滤掉null和undefined

        const unitIdentifier = newItem.unit;

        console.log(`检查商品是否存在 - 标识符列表: [${newItemIdentifiers.join(', ')}], 单位: ${unitIdentifier}, 名称: ${newItem.item_name}`);

        // 详细比较每一个现有商品
        retailOrderInfo.items.forEach((existingItem, idx) => {
          const existingIdentifiers = [
            existingItem.item,
            existingItem.code,
            existingItem.id,
            existingItem.item_id
          ].filter(Boolean);

          const existingUnitIdentifier = existingItem.unit;

          // 检查是否有任何标识符匹配
          const hasMatchingId = newItemIdentifiers.some(id => existingIdentifiers.includes(id));

          console.log(`- 对比现有商品[${idx}] - 标识符列表: [${existingIdentifiers.join(', ')}], 单位: ${existingUnitIdentifier}, 名称: ${existingItem.item_name}`);
          console.log(`  ID匹配: ${hasMatchingId}, 单位匹配: ${existingUnitIdentifier === unitIdentifier}`);
        });

        // 查找匹配项，只要有一个标识符匹配且单位相同就认为是同一个商品
    const existingItemIndex = retailOrderInfo.items.findIndex(
      (existingItem: any) => {
            const existingIdentifiers = [
              existingItem.item,
              existingItem.code,
              existingItem.id,
              existingItem.item_id
            ].filter(Boolean);

            // 检查是否有任何标识符匹配
            const idMatch = newItemIdentifiers.some(id => existingIdentifiers.includes(id));
        const unitMatch = existingItem.unit === unitIdentifier;

            if (idMatch) {
              console.log(`找到ID匹配商品: ${existingItem.item_name}, 单位匹配: ${unitMatch}`);
            }

            return idMatch && unitMatch;
          }
    );

    if (existingItemIndex !== -1) {
      // 如果找到相同商品和相同单位，则用新商品覆盖原有商品
      retailOrderInfo.items[existingItemIndex] = newItem;
        if (existingItemIndex !== -1) {
          // 如果找到相同商品和相同单位，则直接完全覆盖原有商品
          console.log(`找到完全匹配的商品，索引: ${existingItemIndex}`);
          console.log(`原商品: ${JSON.stringify(retailOrderInfo.items[existingItemIndex])}`);
          console.log(`新商品: ${JSON.stringify(newItem)}`);

          // 直接替换，不需要保留任何原有字段
          retailOrderInfo.items[existingItemIndex] = {...newItem};

      console.log(
        `覆盖商品: ${newItem.item_name} (${newItem.unit_name}), 新数量: ${newItem.quantity}`
      );
    } else {
      // 如果没有找到相同商品和单位的组合，则添加为新项目
      retailOrderInfo.items.push(newItem);
      console.log(
        `添加新商品: ${newItem.item_name} (${newItem.unit_name}), 数量: ${newItem.quantity}`
      );
    }
  });

  // 循环旧数组，然后再循环新数组，遇到item相同的元素，计算总量
  // 首先收集所有需要处理的唯一item
  // 找出新数组中存在的 item ID
  const newItemIds = [...new Set(formattedItems.map((item: any) => item.item))];

  newItemIds.forEach(currentItemId => {
    let total = 0;
      // Vue 3 响应式系统会自动更新视图，不需要手动强制更新
      console.log("数据已更新，视图将自动刷新");

      // 延迟检查商品列表是否显示
      setTimeout(() => {
        console.log("更新後的商品列表:", retailOrderInfo.items);
        console.log("商品列表長度:", retailOrderInfo.items.length);
      }, 100);


      // 计算商品总金额
      let totalAmount = 0;
      console.log("计算订单总金额，商品数量:", retailOrderInfo.items.length);

      retailOrderInfo.items.forEach((item : OrderItem , index: number) => {
        const itemTotal = Number(item.quantity || 0) * Number(item.price || 0);
        totalAmount += itemTotal;

        // 记录每个商品的信息，帮助调试
        console.log(`商品${index+1}: ID=${item.item || item.code}, 名称=${item.item_name}, 单位=${item.unit_name}, 数量=${item.quantity}, 单价=${item.price}, 小计=${itemTotal}`);
      });

      retailOrderInfo.total_amount = totalAmount.toFixed(2);

      // 更新折扣和实际支付金额
      calcDiscount();
      calcDiscountRate();

      console.log("更新订单金额:", {
        total_amount: retailOrderInfo.total_amount,
        discount: retailOrderInfo.discount,
        total_sale_price: retailOrderInfo.total_sale_price
      });
    },

    // 循环旧数组，累加所有相同item商品的 remaining_stock
    retailOrderInfo.items.forEach((item: any) => {
      if (item.item === currentItemId) {
        total += Number(item.remaining_stock || 0);
      }
    });

    // 循环新数组，累加所有相同item元素的 quantity 乘以转换率
    // 过滤掉旧数组中已存在的商品
    formattedItems.forEach((newItem: any) => {
      if (newItem.item === currentItemId && !retailOrderInfo.items.some((item: any) => item.item === newItem.item && item.unit === newItem.unit)) {
        total += Number(newItem.quantity || 0) * Number(newItem.conversion_rate || 1);
      }
    });

    // 给旧数组中所有item相同的元素的 remaining_stock 赋值为 total
    retailOrderInfo.items.forEach((item: any) => {
      if (item.item === currentItemId) {
        item.remaining_stock = total;
        console.log("更新后的总数：", total);
        console.log("更新后的商品库存：", item.remaining_stock);
        console.log("更新后的商品数据：", item);
      }
    });
  });

const showQtyArrows = (index: number) => {
  if (isView.value) {
    uni.showToast({
      title: '保存后禁止修改',
      icon: "none",
    })
    return;
  }
  if (activeQtyIndex.value === index) {
    // 如果已经是当前，说明是第二次点击，收起
    activeQtyIndex.value = -1;
  } else {
    // 否则显示
    activeQtyIndex.value = index;
  }
};
const hideQtyArrows = () => {
  activeQtyIndex.value = -1;
};
const changeQuantity = (item: OrderItem, val: number) => {
  // 只更新当前操作的条目quantity
  const currentItemIndex = retailOrderInfo.items.findIndex(
    (infoItem: any) => infoItem.code === item.code && infoItem.unit === item.unit
  );
  if (currentItemIndex === -1) return;
  const currentItem = retailOrderInfo.items[currentItemIndex];

  // 验证库存限制
  if (val > Number(currentItem.stock_remaining)) {
    uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
    val = Number(currentItem.stock_remaining);
  } else if (val * Number(currentItem.conversion_rate) > Number(currentItem.total_stock)) {
    uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
    val = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
  }

  // 更新当前条目的quantity
  currentItem.quantity = val;

  // 直接循环retailOrderInfo.items，计算相同id的商品总用量
  let totalUsedStock = 0;
  retailOrderInfo.items.forEach((item: any) => {
    if (item.code === currentItem.code) {
      totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
    }
  });

  // 将计算出的remainingStock赋值给所有相同id的元素
  retailOrderInfo.items.forEach((item: any) => {
    if (item.code === currentItem.code) {
      item.remaining_stock = totalUsedStock;
    }
  });
  // calcTotalAndDiscount();
};
const decreaseQty = (index: number, item: OrderItem) => {
  if (!retailOrderInfo || !retailOrderInfo.items) return;
  const currentItem = retailOrderInfo.items[index];
  if (currentItem && Number(currentItem.quantity) > 1) {
    // 只减少当前条目的quantity
    currentItem.quantity = Number(currentItem.quantity) - 1;

    // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
    const sameCodeItems = retailOrderInfo.items.filter((i: any) => i.code === currentItem.code);
    const totalStock = sameCodeItems[0].total_stock;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        item.remaining_stock = totalUsedStock;
      }
    });
    // calcTotalAndDiscount();
  } else {
    uni.showToast({ title: "数量不能小于1", icon: "none" });
  }
};
const increaseQty = (index: number, item: OrderItem) => {
  if (!retailOrderInfo || !retailOrderInfo.items) return;
  if (item) {
    const currentItem = retailOrderInfo.items[index];
    // 验证库存限制
    if (Number(currentItem.quantity) == Number(currentItem.stock_remaining)) {
      uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
      return;
    } else if (
      Number(currentItem.quantity * currentItem.conversion_rate) >= Number(currentItem.total_stock)
    ) {
      uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
      currentItem.quantity = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
    } else {
      // 只增加当前条目的quantity
      currentItem.quantity = Number(currentItem.quantity || 0) + 1;
    }

    // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
    const sameCodeItems = retailOrderInfo.items.filter((i: any) => i.code === currentItem.code);
    const totalStock = sameCodeItems[0].total_stock;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        item.remaining_stock = totalUsedStock;
      }
    });
    // calcTotalAndDiscount();
  }
};



/**
 * 计算折扣金额
 * 根据订单总金额和折扣率计算折扣金额，并保留两位小数
 */
const calcDiscountRate = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discountRate = Number(retailOrderInfo.discountRate) || 0;
  console.log(discountRate);

  retailOrderInfo.discount = ((total * discountRate) / 100).toFixed(2);
};

//从商品清单组件回来的数据
// 处理金额变化
const handleAmountChange = (amountData: AmountData) => {
  console.log("收到merchbill金額變更事件:", amountData);

  if (!amountData) {
    console.warn("金額數據為空，不進行更新");
    return;
  }

  // 記錄修改前的金額
  const oldTotalAmount = retailOrderInfo.total_amount;

  // 正確映射從merchbill傳來的數據
  // total_amount: 結算信息的合計金額，其值應與商品清單的實際金額同步
  retailOrderInfo.total_amount = amountData.actualAmount ? Number(amountData.actualAmount).toFixed(2) : '0.00';

  // discount: 商品清單優惠金額 (來自merchbill的discount)
  retailOrderInfo.discount = amountData.discount ? Number(amountData.discount).toFixed(2) : '0.00';

  // total_sale_price: 商品清單實際金額 (來自merchbill的actualAmount)
  retailOrderInfo.total_sale_price = amountData.actualAmount ? Number(amountData.actualAmount).toFixed(2) : '0.00';

  console.log("已同步商品清單數據:", {
    total_amount: retailOrderInfo.total_amount,
    discount: retailOrderInfo.discount,
    total_sale_price: retailOrderInfo.total_sale_price
  });

  // 如果合計金額有變化，則重新計算結算信息的相關字段
  // (這部分邏輯是處理結算信息的優惠，與商品清單的優惠分離)
  if (oldTotalAmount !== retailOrderInfo.total_amount) {
    console.log("合計金額變化，重新計算結算優惠和收款金額");

    // 使用結算信息中的優惠率(discountRate)重新計算收款優惠(payment_method_discount)
    calcDiscountRate();

    // 根據新的收款優惠重新計算收款金額(pay_amount)
    calcActualAmount();

    console.log("重新計算後的結算信息:", {
      payment_method_discount: retailOrderInfo.payment_method_discount,
      pay_amount: retailOrderInfo.pay_amount
    });
  }
};

// 处理关联订单选择
const handleRelatedOrder = (orderData: any) => {
  console.log("处理关联订单选择:", orderData);
  // 这里添加处理关联订单的逻辑
};

// 处理关联订单商品选择
const handleRelatedOrderItems = (itemsData: any) => {
  console.log("处理关联订单商品选择:", itemsData);
  // 这里添加处理关联订单商品的逻辑
};

/**
 * 计算折扣率
 * 根据订单总金额和折扣金额计算折扣率，并保留两位小数
 * 若总金额为 0，则折扣率设为 0
 */
const calcDiscount = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discount = Number(retailOrderInfo.discount) || 0;
  if (total > 0) {
    retailOrderInfo.discountRate = ((discount / total) * 100).toFixed(2);
  } else {
    retailOrderInfo.discountRate = "0";
  }
  calcActualAmount();
};
/**
 * 计算实际支付金额
 * 用订单总金额减去折扣金额得到实际支付金额，并保留两位小数
 */
const calcActualAmount = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discount = Number(retailOrderInfo.discount) || 0;
  const actualAmount = total - discount;
  retailOrderInfo.total_sale_price = actualAmount.toFixed(2);
  retailOrderInfo.total_amount = total.toFixed(2);
};
/**
 * 当折扣率变化时更新相关数据
 * 先计算折扣金额，再更新总金额、实际支付金额、已支付金额和欠款金额
 */
const changeDiscount = () => {
  calcDiscountRate();
  calcActualAmount();
};
/**
 * 当折扣金额变化时更新相关数据
 * 先计算折扣率，再更新总金额、实际支付金额、已支付金额和欠款金额
 */
const changeDiscountRate = () => {
  calcDiscount();
  calcActualAmount();
};


// 获取客户列表
const getCustomersList = () => {
  console.log("开始获取客户列表，参数：", customerPageParams);
  if (!isCustomerMore.value) return;
  getCustomerList(customerPageParams)
    .then((res: any) => {
      console.log("客户列表API返回结果：", res);
      if (res.code == 0) {
        customerList.value = res.data.results;
        console.log(
          "客户列表加载成功，共",
          customerList.value.length,
          "条数据"
        );
        if (customerPageParams.page_size > res.data.results.length) {
          isCustomerMore.value = false;
        } else {
          customerPageParams.page++;
          isCustomerMore.value = true;
        }
      } else {
        console.error("客户列表加载失败：", res.msg);
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    })
    .catch((err: any) => {
      console.error("客户列表接口调用失败：", err);
    });
};
//打开客户选择框
const openSelector = (index: number) => {
  if (isView.value) {
    return;
  }
  console.log("用户点击了选择客户，选择类型：", index);
  selectType.value = index;
  // 如果客户列表为空，先拉取数据
  if (selectType.value === 1 && customerList.value.length === 0) {
    console.log("客户列表为空，正在拉取数据...");
    getCustomersList();
  }
  selectorShow.value = true;
  console.log("选择器已打开，当前状态：", selectorShow.value);
  if (selectType.value === 1) {
    selectList.value = customerList.value;
    console.log("客户选择列表数据：", selectList.value);
  } else {
    // selectList.value = settlementAccountList.value; // settlementAccountList is not defined
  }
};
// 选择客户
const selectCustomer = (item: CustomerItem) => {
  console.log("用户选择了客户：", item);
  if (selectType.value === 1) {
    retailOrderInfo.customer = item.id;
    retailOrderInfo.customer_name = item.name;
    // 手动触发客户字段验证
    nextTick(() => {
      if (uForm.value) {
        (uForm.value as any).validateField("customer_name");
      }
    });
  }
};
// 关闭搜索弹出层
const closePopup = () => {
  console.log("用户关闭了选择器");
  selectorShow.value = false;
};
    /**
     * 當結算信息部分的優惠率輸入框值變化時觸發
     * 重新計算折扣金額和實際金額
     */
    updateDiscountRate() {
      console.log("結算信息-優惠率變化:", retailOrderInfo.discountRate);

      // 計算折扣金額
      const total = Number(retailOrderInfo.total_amount) || 0;
      const rate = Number(retailOrderInfo.discountRate) || 0;

      // 確保優惠率不超過100%
      if (rate > 100) {
        uni.showToast({
          title: "優惠率不能大於100%",
          icon: "none"
        });
        retailOrderInfo.discountRate = "100";
      }

      // 計算折扣金額
      const discount = ((total * rate) / 100).toFixed(2);
      // discount是商品清單優惠金額，保持不變
      // retailOrderInfo.discount 保持不變
      retailOrderInfo.payment_method_discount = discount;

      // 計算收款金額
      const actualAmount = (total - parseFloat(discount)).toFixed(2);
      retailOrderInfo.pay_amount = actualAmount;
      // total_sale_price 是商品清單實際金額，不應在這裡修改
      // retailOrderInfo.total_sale_price 保持不變



      // 制視圖更新
      forceUpdate();
    },

    /**
     * 當結算信息部分的優惠金額輸入框值變化時觸發
     * 重新計算折扣率和實際金額
     */
    updateDiscount() {
      console.log("結算信息-收款優惠變化:", retailOrderInfo.payment_method_discount);

      const total = Number(retailOrderInfo.total_amount) || 0;
      const discount = Number(retailOrderInfo.payment_method_discount) || 0;

      // 確保折扣不超過總金額
      if (discount > total) {
        uni.showToast({
          title: "優惠金額不能大於合計金額",
          icon: "none"
        });
        retailOrderInfo.payment_method_discount = total.toFixed(2);
      }

      // 計算折扣率
      if (total > 0) {
        const rate = ((discount / total) * 100).toFixed(2);
        retailOrderInfo.discountRate = rate;
      } else {
        retailOrderInfo.discountRate = "0";
      }

      // discount是商品清單優惠金額，不應該在這裡修改
      // retailOrderInfo.discount 保持不變

      // 計算收款金額
      const actualAmount = (total - discount).toFixed(2);
      retailOrderInfo.pay_amount = actualAmount;
      // total_sale_price 是商品清單實際金額，不應在這裡修改
      // retailOrderInfo.total_sale_price 保持不變

      // console.log("結算信息-計算結果:", {
      //   合計金額: retailOrderInfo.total_amount,
      //   優惠率: retailOrderInfo.discountRate + "%",
      //   商品優惠: retailOrderInfo.discount,
      //   收款優惠: retailOrderInfo.payment_method_discount,
      //   收款金額: retailOrderInfo.pay_amount,
      //   商品實際金額: retailOrderInfo.total_sale_price
      // });

      // 強制視圖更新
      forceUpdate();
    },

    // 獲取客戶列表
    getCustomersList() {
      console.log("開始獲取客戶列表，參數：", customerPageParams);
      if (isCustomerMore) return;
      getCustomerList(customerPageParams)
        .then((res) => {
          console.log("客戶列表API返回結果：", res);
          if (res.code == 0) {
            customerList = res.data.results;
            console.log(
              "客戶列表加載成功，共",
              customerList.length,
              "條數據"
            );
            if (customerPageParams.page_size > res.data.results.length) {
              isCustomerMore = false;
            } else {
              customerPageParams.page++;
              isCustomerMore = true;
            }
          } else {
            console.error("客戶列表加載失敗：", res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("客戶列表接口調用失敗：", err);
        });
    },
    //打開客戶選擇框
    openSelector(index) {
      if (isView) {
        return;
      }
      console.log("用戶點擊了選擇客戶，選擇類型：", index);
      selectType = index;
      // 如果客戶列表為空，先拉取數據
      if (selectType === 1 && customerList.length === 0) {
        console.log("客戶列表為空，正在拉取數據...");
        getCustomersList();
      }
      selectorShow = true;
      console.log("選擇器已打開，當前狀態：", selectorShow);
      if (selectType === 1) {
        selectList = customerList;
        console.log("客戶選擇列表數據：", selectList);
      } else {
        selectList = settlementAccountList;
      }
    },
    // 選擇客戶
    selectCustomer(item) {
      console.log("用戶選擇了客戶：", item);
      if (selectType === 1) {
        retailOrderInfo.customer = item.id;
        retailOrderInfo.customer_name = item.name;
        // 手動觸發客戶字段驗證
        $nextTick(() => {
          $refs.uForm.validateField("customer_name");
        });
      }
    },
    // 關閉搜索彈出層
    closePopup() {
      console.log("用戶關閉了選擇器");
      selectorShow = false;
    },

// 打开库存选择弹出层
const openInventorySelection = () => {
  if (isView.value) {
    uni.showToast({
      title: '保存后禁止修改',
      icon: "none",
    })
    return;
  }
  inventorySelectionShow.value = true;
};
// 关闭库存选择弹出层
const closeInventorySelection = () => {
  inventorySelectionShow.value = false;
};
// 接收库存弹出层的数据
const confirmInventory = (data: any) => {
  console.log(data);
    // 打開庫存選擇彈出層
    openInventorySelection() {
      if (isView) {
        uni.showToast({
          title: '保存後禁止修改',
          icon: "none",
        })
        return;
      }
      inventorySelectionShow = true;
    },
    // 關閉庫存選擇彈出層
    closeInventorySelection() {
      inventorySelectionShow = false;
    },
    // 接收庫存彈出層的數據
    confirmInventory(data) {
      console.log(data);

  retailOrderInfo.warehouse = data.id;
  retailOrderInfo.warehouse_name = data.name;
  uni.setStorageSync("retailWarehouseId", data.id);
};

// 添加商品
const addGoods = (index: number) => {
  if (isView.value) {
    uni.showToast({
      title: '保存后禁止修改',
      icon: "none",
    })
    return;
  }
  if (!retailOrderInfo.warehouse) {
    uni.showToast({
      title: '请先选择仓库',
      icon: "none",
    });
    return;
  }
  uni.navigateTo({
    url:
      "/pages/purchase/editPurchase/components/productSelection?type=6&isShowWarehouse=true&warehouse=" +
      retailOrderInfo.warehouse +
      "&warehouse_name=" +
      retailOrderInfo.warehouse_name,
  });
};
    // 添加商品
    addGoods(index: number) {
      if (isView) {
        uni.showToast({
          title: '保存後禁止修改',
          icon: "none",
        })
        return;
      }
      if (!retailOrderInfo.warehouse) {
        uni.showToast({
          title: '請先選擇倉庫',
          icon: "none",
        });
        return;
      }

      // 構建URL參數 - 普通添加商品不傳遞關聯訂單ID
      let url = "/components/productSelection?type=10&isShowWarehouse=true" +
        "&warehouse=" + retailOrderInfo.warehouse +
        "&warehouse_name=" + retailOrderInfo.warehouse_name;

      // 注意：這裡不再添加related_order_id參數，確保普通添加的商品不會被標記為銷售訂單商品

      // 跳轉到商品選擇頁面
      uni.navigateTo({
        url: url
      });

      console.log('跳轉到商品選擇頁面，設置類型為銷貨單(type=10)');
    },

//打开结算账户选择
const openAccountSelector = () => {
  if (isView.value) {
    uni.showToast({
      title: '保存后禁止修改',
      icon: "none",
    })
    return;
  }
  accountSelectShow.value = true;
};
//删除结算账户
const removePurchaseOrder = () => {
  retailOrderInfo.payment_method = "";
  retailOrderInfo.payment_method_name = "";
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  retailOrderInfo.payment_method = data.id;
  retailOrderInfo.payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};
    //打開結算賬戶選擇
    openAccountSelector() {
      if (isView) {
        uni.showToast({
          title: '保存後禁止修改',
          icon: "none",
        })
        return;
      }
      accountSelectShow = true;
    },
    //刪除結算賬戶
    removePurchaseOrder() {
      retailOrderInfo.payment_method = "";
      retailOrderInfo.payment_method_name = "";
    },
    //處理結算賬戶的數據
    handleAccount(data) {
      retailOrderInfo.payment_method = data.id;
      retailOrderInfo.payment_method_name = data.account_name;
    },
    //關閉結算賬戶選擇
    closeAccountSelector() {
      accountSelectShow = false;
    },

// 打开保存提示框逻辑
const save = () => {
  // 先检查商品列表
  if (
    !retailOrderInfo.items ||
    retailOrderInfo.items.length === 0
  ) {
    uni.showToast({
      title: "请至少添加一个商品",
      icon: "none",
    });
    return;
  }
  //检查结算账户
  // if (
  //   retailOrderInfo.pay_amount > 0 &&
  //   !retailOrderInfo.payment_method
  // ) {
  //   uni.showToast({
  //     title: "请填写结算账户",
  //     icon: "none",
  //   });
  //   return;
  // }
  // 触发表单校验
  if (uForm.value) {
    (uForm.value as any)
      .validate()
      .then((valid: boolean) => {
        if (valid) {
          // 校验通过
          promptShow.value = true;
        } else {
          // 校验不通过
          uni.showToast({ title: "信息未填写完整", icon: "none" });
        }
      })
      .catch((error: any) => {
        console.error("表单验证错误:", error);
        uni.showToast({ title: "信息未填写完整", icon: "none" });
      });
  }
};
//关闭提示框
const close = () => {
  promptShow.value = false;
};
    // 打開保存提示框邏輯
    save() {
      // 先檢查商品列表
      if (
        !retailOrderInfo.items ||
        retailOrderInfo.items.length === 0
      ) {
        uni.showToast({
          title: "請至少添加一個商品",
          icon: "none",
        });
        return;
      }
      //檢查結算賬戶
      if (
        retailOrderInfo.pay_amount > 0 &&
        !retailOrderInfo.payment_method
      ) {
        uni.showToast({
          title: "請填寫結算賬戶",
          icon: "none",
        });
        return;
      }
      // 觸發表單校驗
      $refs.uForm
        .validate()
        .then((valid) => {
          if (valid) {
            // 校驗通過
            promptShow = true;
          } else {
            // 校驗不通過
            uni.showToast({ title: "信息未填寫完整", icon: "none" });
          }
        })
        .catch((error) => {
          console.error("表單驗證錯誤:", error);
          uni.showToast({ title: "信息未填寫完整", icon: "none" });
        });
    },
    //關閉提示框
    close() {
      promptShow = false;
    },

    // 改名為addWholesaleOrder，更準確地反映功能
    addWholesaleOrder() {
      // 構建符合API要求的数据结构
      const submitData = this.buildSubmitData();
      console.log('提交批发订单数据:', JSON.stringify(submitData, null, 2));

      // 確保订单类型正确
      if (submitData.document_type !== 'wholesale') {
        console.warn('订单类型不是批发订单，正在修正...');
        submitData.document_type = 'wholesale';
      }
//新建逻辑
const addRetailOrdersFunc = () => {
  // 构建符合API要求的数据结构
  const submitData = buildSubmitData();
  console.log('提交的数据:', submitData);

  // 檢查關聯銷售訂單
      if (!submitData.sales_order && retailOrderInfo.related_order_id) {
        console.warn('未設置sales_order字段但有related_order_id，正在修正...');
        submitData.sales_order = parseInt(retailOrderInfo.related_order_id);
      }

      // 添加額外日誌，幫助診斷問題
      // console.log('批发订单关联信息:', {
      //   related_order_id: retailOrderInfo.related_order_id,
      //   related_order_code: retailOrderInfo.related_order_code,
      //   sales_order: submitData.sales_order
      // });

      // console.log('批发订单折扣信息:', {
      //   total_amount: retailOrderInfo.total_amount,
      //   discount: submitData.discount,
      //   discount_rate: submitData.discount_rate,
      //   total_sale_price: submitData.total_sale_price
      // });

      // 顯示加載提示
      uni.showLoading({
        title: '正在保存...',
        mask: true
      });

      // 調用API
      addRetailOrders(submitData).then((res: any) => {
        uni.hideLoading();
    if (res.code == 0) {
      console.log('API返回成功:', res);
          uni.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2000
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 2000);
        } else {
          console.error('API返回错误:', res);
          uni.showToast({
            title: res.msg || "保存失败",
            icon: "none",
            duration: 3000
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('API调用异常:', err);
        uni.showToast({
          title: "系统错误",
          icon: "none",
          duration: 3000
    });
  });
};

// 构建提交数据，确保格式符合API要求
const buildSubmitData = () => {
  const data = {
    customer: parseInt(retailOrderInfo.customer) || null,
    warehouse: parseInt(retailOrderInfo.warehouse) || null,
    document_type: retailOrderInfo.document_type,
    handler: parseInt(retailOrderInfo.handler) || null, // 操作员ID，如果未设置则为null
    total_sale_price: parseFloat(retailOrderInfo.total_sale_price) || 0,
    discount: parseFloat(retailOrderInfo.discount) || 0,
    payment_method: parseInt(retailOrderInfo.payment_method) || null,
    is_draft: Boolean(retailOrderInfo.is_draft),
    remark: retailOrderInfo.remark || "",
    items: retailOrderInfo.items.map((item: any) => ({
      item: parseInt(item.item),
      unit: parseInt(item.unit),
      quantity: parseFloat(item.quantity) || 1,
      price: parseFloat(item.price) || 0
    }))
  };
    // 構建提交數據，確保格式符合API要求
    buildSubmitData() {
      // 在提交前，確保所有商品都有唯一標識
      validateItemIdentifiers();

      // 確保total_sale_price不為空
      if (!retailOrderInfo.total_sale_price) {
        const total = Number(retailOrderInfo.total_amount) || 0;
        const discount = Number(retailOrderInfo.discount) || 0;
        retailOrderInfo.total_sale_price = (total - discount).toFixed(2);
        console.log("提交前計算商品清單實際金額(total_sale_price):", retailOrderInfo.total_sale_price);
      }

      // 確保discount不為空
      if (retailOrderInfo.discount === undefined || retailOrderInfo.discount === null || retailOrderInfo.discount === '') {
        retailOrderInfo.discount = '0.00';
        console.log("提交前發現商品清單優惠金額(discount)為空，設置為0");
      }

      // 在提交前確保優惠金額和優惠率是一致的
      updateDiscount();

      // 最後檢查所有重要金額字段
      ['total_amount', 'discount', 'total_sale_price', 'payment_method_discount', 'pay_amount'].forEach(field => {
        if (retailOrderInfo[field] === undefined || retailOrderInfo[field] === null || retailOrderInfo[field] === '') {
          console.warn(`提交前發現${field}為空，設置為0`);
          retailOrderInfo[field] = '0.00';
        }
      });

      console.log('構建提交數據:', {
        折扣率: retailOrderInfo.discountRate,
        商品優惠金額: retailOrderInfo.discount,
        收款優惠: retailOrderInfo.payment_method_discount,
        商品實際金額: retailOrderInfo.total_sale_price,
        收款金額: retailOrderInfo.pay_amount,
        關聯訂單ID: retailOrderInfo.sales_order
      });

      const data = {
        customer: parseInt(retailOrderInfo.customer) || null,
        warehouse: parseInt(retailOrderInfo.warehouse) || null,
        document_type: retailOrderInfo.document_type,
        handler: parseInt(retailOrderInfo.handler) || null, // 操作員ID
        // 商品清單實際金額
        total_sale_price: parseFloat(retailOrderInfo.total_sale_price) || 0,
        // 商品清單優惠金額
        discount: parseFloat(retailOrderInfo.discount) || 0,
        // 收款優惠
        payment_method_discount: parseFloat(retailOrderInfo.payment_method_discount) || 0,
        // 收款金額
        pay_amount: parseFloat(retailOrderInfo.pay_amount) || 0,
        // 優惠率
        discount_rate: parseFloat(retailOrderInfo.discountRate) || 0,
        payment_method: parseInt(retailOrderInfo.payment_method) || null,
        is_draft: Boolean(retailOrderInfo.is_draft),
        remark: retailOrderInfo.remark || "",
        items: retailOrderInfo.items.map((item :OrderItem) => {
          // 優先使用商品ID而不是條碼
          const itemId = parseInt(item.item) || parseInt(item.id) || parseInt(item.code) || 0;

          if (!itemId) {
            console.error('商品缺少有效的ID:', item);
          }

          return {
            item: itemId, // 使用正確的商品ID
            unit: parseInt(item.unit) || 0,
            quantity: parseFloat(item.quantity) || 1,
            price: parseFloat(item.price) || 0
          };
        })
      };

  // 如果有关联的销售订单，添加sales_order字段
  if (retailOrderInfo.sales_order) {
    (data as any).sales_order = parseInt(retailOrderInfo.sales_order);
        console.log('添加关联销售订单字段:', data.sales_order);
      } else if (retailOrderInfo.related_order_id) {
        // 如果没有设置sales_order但有related_order_id，也使用它
        data.sales_order = parseInt(retailOrderInfo.related_order_id);
        console.log('使用related_order_id作为sales_order:', data.sales_order);
      }

  return data;
};
      console.log('构建的提交数据:', data);
      return data;
    },

    // 驗證並確保所有商品都有正確的唯一標識
    validateItemIdentifiers() {
      if (!retailOrderInfo.items || retailOrderInfo.items.length === 0) return;

      // 檢查是否有重複的商品(相同商品ID和單位)
      const itemMap = new Map();
      const duplicates = [];

      retailOrderInfo.items.forEach((item, index) => {
        // 確保優先使用id字段，如果沒有則使用item或code字段
        if (!item.item) {
          if (item.id) {
            console.log(`商品 ${item.item_name} 使用id(${item.id})作為item值`);
            retailOrderInfo.items[index].item = item.id;
          } else if (item.code) {
            console.log(`商品 ${item.item_name} 使用code(${item.code})作為item值`);
            retailOrderInfo.items[index].item = item.code;
          }
        }

        const itemIdentifier = item.item || item.id || item.code;
        const unitIdentifier = item.unit;
        const key = `${itemIdentifier}-${unitIdentifier}`;

        if (itemMap.has(key)) {
          // 找到重複商品
          duplicates.push({
            index,
            key,
            item
          });
        } else {
          itemMap.set(key, {
            index,
            item
          });
        }
      });

      // 處理重複商品(實際項目中可能需要合併或報警)
      if (duplicates.length > 0) {
        console.warn(`發現${duplicates.length}個重複商品，請檢查:`, duplicates);
      }
    }
};

//刪除倉庫
const removeWarehouseName = () => {
  if (isView.value) {
    uni.showToast({
      title: '保存後禁止修改',
      icon: "none",
    })
    return;
  }
  retailOrderInfo.warehouse = "";
  retailOrderInfo.warehouse_name = "";
  retailOrderInfo.items = [];
  uni.removeStorageSync("retailWarehouseId");
};
//刪除結算賬戶
const removePaymentMethodName = () => {
  retailOrderInfo.payment_method = "";
  retailOrderInfo.payment_method_name = "";
};
// 打開關聯訂單選擇彈出層
const openRelatedOrder = () => {
  if (isView.value) {
    uni.showToast({
      title: '保存後禁止修改',
      icon: "none",
    })
    return;
  }

  // 檢查是否已選擇客戶和倉庫
  if (!retailOrderInfo.customer) {
    uni.showToast({
      title: '請先選擇客戶',
      icon: "none",
    });
    return;
  }

  // 構建傳遞給關聯訂單選擇頁面的參數
  const params = {
    customer: retailOrderInfo.customer,
    warehouse: retailOrderInfo.warehouse || '',
    warehouse_id: retailOrderInfo.warehouse || '' // 添加warehouse_id用于API調用
  };

  console.log('传递给关联页面的参数:', params);

  uni.navigateTo({
    url: `/pages/wholesaleOrders/editWholesaleOrders/wholesaleAssociation?returnGoodsInfo=${JSON.stringify(params)}`
  });
};
// 接收关联订单弹出层的数据
const handleRelatedOrder = (data: any) => {
  console.log('接收到关联订单数据:', data);

  if (!data) {
    console.warn('关联订单数据为空');
    return;
  }

  // 设置关联订单信息
  retailOrderInfo.related_order_id = data.id || '';
  retailOrderInfo.related_order_code = data.order_id || data.id || '';
  retailOrderInfo.related_order_name = data.customer_name || '';
  retailOrderInfo.sales_order = data.id || ''; // 设置API需要的sales_order字段

  // 如果还没有选择客户，自动设置为关联订单的客户
  if (!retailOrderInfo.customer && data.customer) {
    retailOrderInfo.customer = data.customer;
    retailOrderInfo.customer_name = data.customer_name;
  }

  // 如果还没有选择仓库，自动设置为关联订单的仓库
  if (!retailOrderInfo.warehouse && data.warehouse) {
    retailOrderInfo.warehouse = data.warehouse;
    retailOrderInfo.warehouse_name = data.warehouse_name;
  }

  // 如果关联订单有操作员信息，也可以设置
  if (data.handler) {
    retailOrderInfo.handler = data.handler;
    retailOrderInfo.handler_name = data.handler_name;
  }

  console.log('关联订单处理完成');
};
// 删除关联订单
const removeRelatedOrder = () => {
  retailOrderInfo.related_order_code = "";
  retailOrderInfo.related_order_name = "";
  retailOrderInfo.related_order_id = "";
  retailOrderInfo.sales_order = ""; // 清除API字段
};

// 處理關聯訂單商品數據
const handleRelatedOrderItems = (data: any) => {
  console.log('接收到关联订单商品数据:', data);

  if (!data || !data.items || data.items.length === 0) {
    console.warn('关联订单商品数据为空');
    uni.showToast({
      title: '关联订单没有商品信息',
      icon: 'none'
    });
    return;
  }

  const items = data.items;
      console.log('关联订单商品数量:', items.length);

      // 存儲關聯訂單的商品信息，以便在添加關聯商品時使用
      try {
        const orderItems = {
          items: items,
          orderDetail: {
            warehouse_id: retailOrderInfo.warehouse,
            warehouse_name: retailOrderInfo.warehouse_name
          }
        };
        uni.setStorageSync('related_order_items', JSON.stringify(orderItems));
        console.log('已存储关联订单商品数据，数量:', items.length);
      } catch (e) {
        console.error('存储关联订单商品数据失败:', e);
      }

      // 不做任何額外處理，商品的標記已經在wholesaleAssociation.vue中添加了
      console.log('关联订单商品处理完成');
};

onReady(() => {
  if (uForm.value) {
    (uForm.value as any).setRules(rules);
  }
});

onMounted(() => {
  getCustomersList();
  eventBus.$on("selectGoodsList", handleSelectGoodsList);
  eventBus.$on("relatedOrderSelected", handleRelatedOrder);
  eventBus.$on("relatedOrderItemsSelected", handleRelatedOrderItems);
});

onBeforeUnmount(() => {
  eventBus.$off("selectGoodsList", handleSelectGoodsList);
  eventBus.$off("relatedOrderSelected", handleRelatedOrder);
  eventBus.$off("relatedOrderItemsSelected", handleRelatedOrderItems);
});

onLoad((options: any) => {
  console.log('编辑页面 onLoad options:', options);

  // 如果有传入订单ID，则加载订单详情进行编辑
  if (options.data) {
    try {
      // 先解码 URL 编码的数据，再解析 JSON
      const decodedData = decodeURIComponent(options.data);
      console.log('解码后的数据:', decodedData);

      const data = JSON.parse(decodedData);
      console.log('解析后的订单数据:', data);

      Object.assign(retailOrderInfo, data);
      retailOrderInfo.total_amount = Number(data.discount) + Number(data.total_sale_price);
      retailOrderInfo.discountRate = (Number(data.discount) / Number(data.total_amount)) * 100;

      // 设置页面标题为编辑模式
      uni.setNavigationBarTitle({
        title: "编辑批发订单",
      });
      isView.value = true;

      console.log('编辑模式初始化完成');
    } catch (error) {
      console.error('解析订单数据失败:', error);
      uni.showToast({
        title: '数据解析失败',
        icon: 'none'
      });
      // 解析失败时，按新增模式处理
      uni.setNavigationBarTitle({
        title: "新增批发订单",
      });
    }
  } else {
    // 设置页面标题为新增模式
    console.log('新增模式初始化');
    uni.setNavigationBarTitle({
      title: "新增批发订单",
    });
  }

  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  retailOrderInfo.out_date = `${year}-${month}-${day}`;
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
}

.form {
  padding: 0 20rpx;
  width: 95%;
  margin: 0 auto;
}

.goods-list {
  padding: 0 20rpx;
}

.goods_item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  padding: 0 0 0 0;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  position: relative;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_extra {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_x {
  font-size: 15px;
  font-weight: bold;
  margin-right: 2px;
}

.qty_control {
  display: flex;
  align-items: center;
  position: relative;
}

.qty_input {
  width: 40px;
  height: 22px;
  border: 1px solid #222;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin-left: 2px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
  margin-left: 30rpx;

  .total-row-right {
    display: flex;
    align-items: center;
  }

  text:nth-child(1) {
    width: 80px;
    margin-left: 30rpx;
  }
}

.telescoping {
  display: flex;
  justify-content: center;
  align-items: center;
}

.remark {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.upload-area {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}

.stock-tag {
  display: inline-block;
  border: 1px solid #ffa940;
  color: #ffa940;
  border-radius: 12px;
  font-size: 20rpx;
  padding: 0 12rpx;
  margin-left: 10rpx;
  line-height: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}
</style>

